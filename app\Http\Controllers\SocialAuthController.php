<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class SocialAuthController extends Controller
{
    public function redirectToGoogle($user_type = null)
    {
        session(['user_type' => $user_type]);
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            $user_type = session('user_type', 'customer');
            $email = $googleUser->getEmail();
            session(['social_email' => $email, 'social_user_type' => $user_type]);
            return redirect()->route('register')
                ->with('skip_to_step', '2')
                ->with('social_email', $email)
                ->with('social_user_type', $user_type);
        } catch (\Exception $e) {
            return redirect('/register')->withErrors(['error' => 'Google login failed: ' . $e->getMessage()]);
        }
    }

    public function redirectToApple($user_type = null)
    {
        session(['user_type' => $user_type]);
        return Socialite::driver('apple')->redirect();
    }

    public function handleAppleCallback()
    {
        try {
            $appleUser = Socialite::driver('apple')->user();
            $user_type = session('user_type', 'customer');
            $email = $appleUser->getEmail();
            session(['social_email' => $email, 'social_user_type' => $user_type]);
            return redirect()->route('register')
                ->with('skip_to_step', '2')
                ->with('social_email', $email)
                ->with('social_user_type', $user_type);
        } catch (\Exception $e) {
            return redirect('/register')->withErrors(['error' => 'Apple login failed: ' . $e->getMessage()]);
        }
    }
}








