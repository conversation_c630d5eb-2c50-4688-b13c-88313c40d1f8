<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\Holiday;
use App\Http\Requests\HolidayRequest;
use App\Models\Country;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class HolidaysController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('permission:holidays-list|holidays-create|holidays-edit|holidays-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:holidays-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:holidays-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:holidays-delete', ['only' => ['destroy']]);
        $this->middleware('permission:holidays-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $holidays = Holiday::all();
        $countries = Country::all();
        return view('dashboard.admin.holidays.holidays', [
            'holidays' => $holidays,
            'countries' => $countries
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('holidays.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  HolidayRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(HolidayRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'status' => 'required',
            'country_id' => 'required',
            'date' => 'required',
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $holidayData = $validator;
            Holiday::create($holidayData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Holiday Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        return view('holidays.show', ['holiday' => $holiday]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        return response()->json($holiday);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  HolidayRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(HolidayRequest $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'status' => 'required',
            'country_id' => 'required',
            'date' => 'required',
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $holiday = Holiday::where('ids', $id)->firstOrFail();
            $holidayData = $validator;
            $holiday->update($holidayData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Created", "message" => 'Holiday updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        $holiday->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Holiday deleted successfully'
        ]);
    }
}
