<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GoogleCalendar extends Model
{
    use HasFactory;

    protected $table = 'google_calenders';

    protected $fillable = [
        'user_id',
        'google_access_token',
        'google_refresh_token',
        'google_token_expires_at',
        'google_calendar_connected',
    ];

    protected $casts = [
        'google_token_expires_at' => 'datetime',
        'google_calendar_connected' => 'boolean',
    ];

    /**
     * Get the user that owns the Google Calendar connection.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the access token is expired
     */
    public function isTokenExpired()
    {
        return $this->google_token_expires_at && $this->google_token_expires_at->isPast();
    }

    /**
     * Get the access token as array
     */
    public function getAccessTokenArray()
    {
        return $this->google_access_token ? json_decode($this->google_access_token, true) : null;
    }

    /**
     * Set the access token from array
     */
    public function setAccessTokenFromArray($tokenArray)
    {
        $this->google_access_token = json_encode($tokenArray);

        if (isset($tokenArray['expires_in'])) {
            $this->google_token_expires_at = now()->addSeconds($tokenArray['expires_in']);
        }

        if (isset($tokenArray['refresh_token'])) {
            $this->google_refresh_token = $tokenArray['refresh_token'];
        }

        $this->google_calendar_connected = true;
        $this->save();
    }
}
