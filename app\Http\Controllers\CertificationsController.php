<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Models\Certification;
use App\Http\Requests\CertificationRequest;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Validator;

class CertificationsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('permission:certifications-list|certifications-create|certifications-edit|certifications-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:certifications-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:certifications-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:certifications-delete', ['only' => ['destroy']]);
        $this->middleware('permission:certifications-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $certifications = Certification::all();
        return view('dashboard.admin.certifications.certifications', ['certifications' => $certifications]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('certifications.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  CertificationRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(CertificationRequest $request)
    {
        $validator = Validator::make($request->all(),[
            'name' => 'required|string|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $certificationData = $validator;
            if ($request->hasFile("avatar")) {
                $certificationData['image'] = $this->storeImage("certification-images", $request->file('avatar'));
            }
            Certification::create($certificationData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Certification added successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $certification = Certification::where('ids', $id)->firstOrFail();
        return view('certifications.show', ['certification' => $certification]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $certification = Certification::where('ids', $id)->firstOrFail();
        return response()->json($certification);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  CertificationRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(CertificationRequest $request, $id)
    {
        $validator = Validator::make($request->all(),[
            'name' => 'required|string|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $certification = Certification::where('ids', $id)->firstOrFail();
            $certificationData = $validator;
            if ($request->hasFile('avatar')) {
                $this->deleteImage($certification->image);
                $certificationData['image'] = $this->storeImage('certification-images', $request->file('avatar'));
            }
            $certification->update($certificationData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Created", "message" => 'Certification updated successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $certification = Certification::where('ids', $id)->firstOrFail();
        $this->deleteImage($certification->image);
        $certification->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Certification deleted successfully'
        ]);
    }
}
