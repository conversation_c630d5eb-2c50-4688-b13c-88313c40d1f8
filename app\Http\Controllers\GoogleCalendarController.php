<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\GoogleCalendar;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class GoogleCalendarController extends Controller
{
    protected $clientId;
    protected $clientSecret;
    protected $redirectUri;

    public function __construct()
    {
        $this->clientId = config('services.google_calendar.client_id');
        $this->clientSecret = config('services.google_calendar.client_secret');
        $this->redirectUri = config('services.google_calendar.redirect');
    }

    public function redirectToGoogle()
    {
        $query = http_build_query([
            'client_id' => $this->clientId,
            'redirect_uri' => $this->redirectUri,
            'response_type' => 'code',
            'scope' => 'https://www.googleapis.com/auth/calendar.readonly',
            'access_type' => 'offline',
            'prompt' => 'consent',
        ]);

        return redirect('https://accounts.google.com/o/oauth2/auth?' . $query);
    }

    public function handleGoogleCallback(Request $request)
    {
        $response = Http::asForm()->post('https://oauth2.googleapis.com/token', [
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'redirect_uri' => $this->redirectUri,
            'grant_type' => 'authorization_code',
            'code' => $request->code,
        ]);

        $tokens = $response->json();

        if (isset($tokens['access_token'])) {
            GoogleCalendar::updateOrCreate(
                ['user_id' => Auth::id()],
                [
                    'google_access_token' => $tokens['access_token'],
                    'google_refresh_token' => $tokens['refresh_token'] ?? null,
                    'google_token_expires_at' => now()->addSeconds($tokens['expires_in']),
                    'google_calendar_connected' => true,
                ]
            );

            return redirect('/register/professional')->with([
                'type' => 'success',
                'title' => 'Done',
                'message' => 'Google Calendar connected!'
            ]);
        }

       return redirect('/register/professional')->with('error', '!Google Calendar Failed to connect!');
    }
}
