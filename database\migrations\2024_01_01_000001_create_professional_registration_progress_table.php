<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('professional_registration_progress', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->integer('current_step')->default(1);
            $table->json('step_1_data')->nullable();
            $table->json('step_2_data')->nullable();
            $table->json('step_3_data')->nullable();
            $table->json('step_4_data')->nullable();
            $table->json('step_5_data')->nullable();
            $table->boolean('step_1_completed')->default(false);
            $table->boolean('step_2_completed')->default(false);
            $table->boolean('step_3_completed')->default(false);
            $table->boolean('step_4_completed')->default(false);
            $table->boolean('step_5_completed')->default(false);
            $table->boolean('registration_completed')->default(false);
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('professional_registration_progress');
    }
};
