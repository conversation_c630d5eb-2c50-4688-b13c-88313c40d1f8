<div class="container form-card">
    <div class="row">
        <div class="col-12 mb-3">
            <h2 class="fs-title">Qualifications & Certifications</h2>
            <p>Add your qualifications and certifications for verification.</p>
        </div>

        <div class="col-md-12">
            <p>Product Certification</p>
            <div class="custom-checkbox-group mb-3 gray-card">
                @foreach ($certifications as $certification)
                    <label class="custom-checkbox">
                        <input type="checkbox" name="product_certifications[]" value="{{ $certification->id }}"
                            {{ auth()->user()->product_cerficates->contains($certification->id) ? 'checked' : '' }}>
                        <span class="checkbox-label">{{ $certification->name ?? '' }}</span>
                    </label>
                @endforeach
            </div>
        </div>
    </div>

    @if (auth()->user()->certificates->count() == 0)
        <div class="gray-card mt-5">
            <div class="row">
                <h1>Certificate #1</h1>
                <div class="col-md-12">
                    <label class="fieldlabels">Certification Title*</label>
                    <input type="text" name="certificates[0][title]" placeholder="Enter certification title">

                    <label class="fieldlabels">Issued by*</label>
                    <input type="text" name="certificates[0][issued_by]" placeholder="Enter name">
                </div>

                <div class="col-md-6">
                    <label class="fieldlabels">Issued Date*</label>
                    <input type="date" name="certificates[0][issued_date]" placeholder="Enter certification title">
                </div>

                <div class="col-md-6">
                    <label class="fieldlabels">End Date*</label>
                    <input type="date" name="certificates[0][end_date]" placeholder="Enter certification title">
                </div>

                <div class="col-md-12 form-border">
                    <p class="manrope fw-600 light-black">Share Certificates</p>
                    <div class="file-upload-group">
                        <div class="file-upload-group">
                            <label class="upload-box">
                                <img src="http://127.0.0.1:8000/website/assets/images/upload.svg" alt="Upload Icon">
                                <p>Upload Certificate</p>
                                <p class="mb-0">Maximum file size: 2 MB</p>
                                <p>Supported format: JPG and PNG</p>
                                <span class="add-file">
                                    <p class="upload-cert-btn no_validate">Upload</p>
                                </span>
                                <input type="file" name="certificates[0][image]" class="file-input no_validate"
                                    hidden="">
                            </label>
                            <div class="preview-container"></div>
                        </div>
                    </div>


                    <div class="exception-checkbox">
                        <label class="cert-excep">
                            <input type="checkbox" name="certificates[0][exception]" id="exceptionToggle">
                            <span class="">Certificate Exception</span>
                        </label>

                        <div class="exception-textarea">
                            <label class="mb-2" for="w3review">Reason for Exception</label>
                            <textarea class="mb-0 no_validate" id="w3review" name="certificates[0][exception_reason]" rows="4" cols="50"
                                placeholder="Write reason for exception"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="col-md-12">
        <div id="certifications-wrapper">
            @foreach (auth()->user()->certificates ?? [] as $index => $certificate)
                <div class="gray-card my-5 file-upload-group">
                    <h1>Certificate #{{ $loop->iteration }}</h1>
                    <div class="col-md-12">
                        <label class="fieldlabels">Certification Title*</label>
                        <input type="text" name="certificates[{{ $index }}][title]" placeholder="Enter certification title" value="{{ $certificate->title }}">
                        <label class="fieldlabels">Issued by*</label>
                        <input class="no_validate" type="text" name="certificates[{{ $index }}][issued_by]" value="{{ $certificate->issued_by }}"
                            placeholder="Enter name">
                    </div>

                    <div class="col-md-6">
                        <label class="fieldlabels">Issued Date*</label>
                        <input class="no_validate" type="date" name="certificates[{{ $index }}][issued_date]" value="{{ $certificate->issued_date }}"
                            placeholder="Enter issued date">
                    </div>

                    <div class="col-md-6">
                        <label class="fieldlabels">End Date*</label>
                        <input class="no_validate" type="date" name="certificates[{{ $index }}][end_date]" value="{{ $certificate->end_date }}"
                            placeholder="Enter end date">
                    </div>

                    <div class="col-md-12 form-border">
                        <p class="manrope fw-600 light-black">Share Certificates</p>
                        <div>
                            <label class="upload-box fs-12 normal fw-300 Plus-Jakarta-Sans" style="cursor:pointer;">
                                <img src="http://127.0.0.1:8000/website/assets/images/upload.svg" alt="Upload Icon">
                                <p>Upload Certificate</p>
                                <p class="mb-0">Maximum file size: 2 MB</p>
                                <p>Supported format: JPG and PNG</p>
                                <span class="add-file">
                                    <p class="upload-cert-btn fs-14 fw-600"> Upload </p>
                                </span>
                                <input class="no_validate" name="certificates[{{ $index }}][image]" type="file" hidden="">
                            </label>
                        </div>
                        <div class="preview-container"></div>

                        <div class="exception-checkbox">
                            <label class="cert-excep">
                                <input class="no_validate" type="checkbox" id="exceptionToggle"
                                    name="certificates[{{ $index }}][exception]" {{ $certificate->exception ? 'checked' : '' }}>
                                <span class="checkmark">Certificate Exception</span>
                            </label>

                            <div class="exception-textarea">
                                <label class="mb-2" for="w3review_1">Reason for Exception</label>
                                <textarea class="mb-0 no_validate" id="w3review_1" name="certificates[{{ $index }}][exception_reason]" rows="4"
                                    cols="50" placeholder="Write reason for exception"> {{ $certificate->exception_reason }}</textarea>
                            </div>
                        </div>
                    </div>

                    @if (!$loop->first)
                        <div class="mt-3 d-flex justify-content-between">
                            <button type="button" class=" delete-block">Delete This Block</button>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
        <button type="button" id="addMoreBtn" class="addMoreBtn blue-text mt-3"> <span><i
                    class="fas fa-plus"></i></span> Add More</button>
    </div>
</div>
