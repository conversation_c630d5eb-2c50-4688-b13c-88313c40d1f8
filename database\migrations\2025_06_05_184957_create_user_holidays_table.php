<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_holidays', function (Blueprint $table) {
            $table->id();
            $table->integer("user_id");
            $table->integer("holiday_id")->nullable();
            $table->string("name");
            $table->date("date");
            $table->time("start_time")->nullable();
            $table->time("end_time")->nullable();
            $table->integer("is_custom");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_holidays');
    }
};
