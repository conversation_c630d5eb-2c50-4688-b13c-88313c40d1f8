<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use App\Models\SubCategory;
use App\Http\Requests\SubCategoryRequest;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SubCategoriesController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('permission:subcategories-list|subcategories-create|subcategories-edit|subcategories-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:subcategories-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:subcategories-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:subcategories-delete', ['only' => ['destroy']]);
        $this->middleware('permission:subcategories-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $subcategories = SubCategory::all();
        return view('subcategories.index', ['subcategories' => $subcategories]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('subcategories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  SubCategoryRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(SubCategoryRequest $request)
    {
        $alreadyAdded = SubCategory::where('name', $request->name)->exists();
        if ($alreadyAdded) {
            return back()->with([
                'type' => 'warning',
                'message' => 'You have already added this Sub-Category',
                'title' => 'Already added'
            ]);
        }
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'status' => 'required',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'image_description' => 'required',
            'alt_tag' => 'required',
            'category_id' => 'required',
        ]);
                if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $subCategoryData = $validator;
            if ($request->hasFile("avatar")) {
                $subCategoryData['image'] = $this->storeImage("subcategory-images", $request->file('avatar'));
            }
            $subCategoryData['slug'] = Str::slug($request->input('name'));
            SubCategory::create($subCategoryData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Sub-Category Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $subcategory = SubCategory::where('ids', $id)->firstOrFail();
        return view('subcategories.show', ['subcategory' => $subcategory]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $subcategory = SubCategory::where('ids', $id)->firstOrFail();
        return response()->json($subcategory);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  SubCategoryRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(SubCategoryRequest $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'status' => 'required',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'image_description' => 'required',
            'alt_tag' => 'required',
            'category_id' => 'required',
        ]);
                if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $subCategory = SubCategory::where('ids', $id)->firstOrFail();
            $subCategoryData = $validator;
            if ($request->hasFile('avatar')) {
                $this->deleteImage($subCategory->image);
                $subCategoryData['image'] = $this->storeImage('subcategory-images', $request->file('avatar'));
            }
            if ($subCategory->name !== $request->input('name')) {
                $subCategoryData['slug'] = Str::slug($request->input('name'));
            }
            $subCategory->update($subCategoryData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Created", "message" => 'Sub-Category Updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $subCategory = SubCategory::where('ids', $id)->firstOrFail();
        $this->deleteImage($subCategory->image);
        $subCategory->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Category deleted successfully'
        ]);
    }

    public function subUpdateStatus(Request $request)
    {
        $subcategory = SubCategory::findOrFail($request->subcategory_id);
        $subcategory->status = $request->status;
        $subcategory->save();
        return response()->json(['success' => true]);
    }
}
