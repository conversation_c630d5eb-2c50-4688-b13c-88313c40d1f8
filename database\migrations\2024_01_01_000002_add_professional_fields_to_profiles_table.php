<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('profiles', function (Blueprint $table) {
            // Step 1 fields
            $table->string('full_name')->nullable();
            $table->string('company_name')->nullable();
            $table->string('website')->nullable();
            $table->string('facebook')->nullable();
            $table->string('instagram')->nullable();
            $table->string('tiktok')->nullable();
            $table->string('location')->nullable();
            $table->decimal('lat', 10, 8)->nullable();
            $table->decimal('lng', 11, 8)->nullable();
            $table->integer('location_service')->nullable();
            $table->string('company_id')->nullable();
            $table->string('vat_number')->nullable();
            $table->string('avatar')->nullable();
            
            // Step 2 fields (services will be handled separately)
            $table->json('services')->nullable();
            
            // Step 3 fields
            $table->json('certifications')->nullable();
            $table->json('custom_certifications')->nullable();
            
            // Step 4 fields
            $table->json('availability')->nullable();
            $table->json('holidays')->nullable();
            
            // Step 5 fields
            $table->string('banner_image')->nullable();
            $table->json('gallery_images')->nullable();
            
            // Banking information
            $table->string('bank_name')->nullable();
            $table->string('account_number')->nullable();
            $table->string('sort_code')->nullable();
            $table->string('iban')->nullable();
            $table->string('swift')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('profiles', function (Blueprint $table) {
            $table->dropColumn([
                'full_name', 'company_name', 'website', 'facebook', 'instagram', 
                'tiktok', 'location', 'lat', 'lng', 'location_service', 
                'company_id', 'vat_number', 'avatar', 'services', 'certifications',
                'custom_certifications', 'availability', 'holidays', 'banner_image',
                'gallery_images', 'bank_name', 'account_number', 'sort_code',
                'iban', 'swift'
            ]);
        });
    }
};
