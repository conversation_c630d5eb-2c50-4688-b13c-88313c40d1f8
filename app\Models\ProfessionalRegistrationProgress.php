<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProfessionalRegistrationProgress extends Model
{
    protected $table = 'professional_registration_progress';
    
    protected $fillable = [
        'user_id',
        'current_step',
        'step_1_data',
        'step_2_data',
        'step_3_data',
        'step_4_data',
        'step_5_data',
        'step_1_completed',
        'step_2_completed',
        'step_3_completed',
        'step_4_completed',
        'step_5_completed',
        'registration_completed'
    ];

    protected $casts = [
        'step_1_data' => 'array',
        'step_2_data' => 'array',
        'step_3_data' => 'array',
        'step_4_data' => 'array',
        'step_5_data' => 'array',
        'step_1_completed' => 'boolean',
        'step_2_completed' => 'boolean',
        'step_3_completed' => 'boolean',
        'step_4_completed' => 'boolean',
        'step_5_completed' => 'boolean',
        'registration_completed' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getStepData($step)
    {
        return $this->{"step_{$step}_data"} ?? [];
    }

    public function setStepData($step, $data)
    {
        $this->{"step_{$step}_data"} = $data;
        $this->{"step_{$step}_completed"} = true;
        
        // Update current step to next step if not already ahead
        if ($this->current_step <= $step) {
            $this->current_step = min($step + 1, 5);
        }
        
        $this->save();
    }

    public function isStepCompleted($step)
    {
        return $this->{"step_{$step}_completed"} ?? false;
    }

    public function getCompletedStepsCount()
    {
        $completed = 0;
        for ($i = 1; $i <= 5; $i++) {
            if ($this->isStepCompleted($i)) {
                $completed++;
            }
        }
        return $completed;
    }

    public function markRegistrationComplete()
    {
        $this->registration_completed = true;
        $this->save();
    }
}
