<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role;

class Profile extends Model
{
    protected $guarded= [];

    protected $fillable = [
        'user_id', 'bio', 'gender','dob','age','pic','country','state','city','address','postal',
        'full_name', 'company_name', 'phone', 'website', 'facebook', 'instagram', 'tiktok',
        'location', 'lat', 'lng', 'location_service', 'company_id', 'vat_number', 'avatar',
        'services', 'certifications', 'custom_certifications', 'availability', 'holidays',
        'banner_image', 'gallery_images', 'bank_name', 'account_number', 'sort_code',
        'iban', 'swift'
    ];

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function user_roles(){
        return $this->belongsTo(Role::class);
    }
}