<?php $__env->startSection('content'); ?>

    <div class="container-fluid login-page">
        <div class="row">
            <div class="col-md-6 px-lg-20 px-md-20 px-sm-10 d-flex flex-column align-items-center justify-content-center my-10">
                <form class="form w-100" novalidate="novalidate" method="POST" action="<?php echo e(route('login')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="text-center mb-8">
                        <h1 class="text-dark fw-bolder mb-3">Sign In</h1>
                        <div class="text-gray-500 fw-semibold fs-6">Your Social Campaigns</div>
                    </div>
                    <div class="row g-3 mb-9">
                        <div class="col-md-6">
                            <a href="#"
                                class="btn btn-flex btn-outline btn-text-gray-700 bg-state-light flex-center text-nowrap w-100">
                                <img alt="Logo" src="<?php echo e(asset('website/assets')); ?>/images/Google_Logo.svg" class="h-15px me-3" />Sign in with Google
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="#"
                                class="btn btn-flex btn-outline btn-text-gray-700 bg-state-light flex-center text-nowrap w-100">
                                <img alt="Logo" src="<?php echo e(asset('website/assets')); ?>/images/Apple_Logo.svg"
                                class="theme-light-show h-15px me-3" />
                                <img alt="Logo" src="<?php echo e(asset('website/assets')); ?>/images/Apple_Logo.svg"
                                class="theme-dark-show h-15px me-3" />Sign in with Apple
                            </a>
                        </div>
                    </div>
                    <div class="separator separator-content my-8">
                        <span class="w-125px text-gray-500 fw-semibold fs-7">Or with email</span>
                    </div>
                    <div class="fv-row mb-8">
                        <input id="email" type="email" placeholder="Email"
                            class="form-control  bg-transparent <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email"
                            value="<?php echo e(old('email')); ?>" required autocomplete="email" autofocus>
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                <strong><?php echo e($message); ?></strong>
                            </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="fv-row mb-3 position-relative">
                        <input id="password" type="password" placeholder="Password"
                            class="form-control  bg-transparent <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="password"
                            required autocomplete="current-password">

                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                <strong><?php echo e($message); ?></strong>
                            </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>


                        <span id="toggle-password"
                            class=" btn-sm btn-icon position-absolute translate-middle end-0 pb-12 pe-2 ">
                            <i class="fa-solid fa-eye"></i>
                            <i class="fa-solid fa-eye-slash d-none"></i>
                        </span>



                    </div>
                    <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
                        <div></div>
                        <a href="<?php echo e(route('password.request')); ?>" class="blue-text">Forgot Password ?</a>
                    </div>

                    <div class="d-grid mb-6">
                        <button type="submit" id="kt_sign_in_submit" class="blue-btn">
                            <span class="indicator-label">Sign In</span>
                            <span class="indicator-progress">Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                        </button>
                    </div>

                    <div class="text-gray-500 text-center fw-semibold fs-6">Not a Member yet?
                        <a href="<?php echo e(route('register')); ?>" class="blue-text">Sign up</a>
                    </div>
                </form>

                <div class="site_logo pt-10">
                    <a href="<?php echo e(url('/')); ?>" class="text-center">
                        <img src="<?php echo e(asset('website')); ?>/assets/images/logo.png" alt="icon">
                        <h4 class="blue-text pt-2"> Stylenest </h4>
                    </a>
                </div>
            </div>

            <div class="col-md-6 login-side-image">
                <img src="<?php echo e(asset('website')); ?>/assets/images/login-banner.png" alt="icon">
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <script>
        $(document).ready(function () {
            $('#toggle-password').on('click', function () {
                var passwordField = $('#password');
                var passwordFieldType = passwordField.attr('type');

                if (passwordFieldType === 'password') {
                    passwordField.attr('type', 'text');
                    $(this).find('.fa-eye-slash').removeClass('d-none');
                    $(this).find('.fa-eye').addClass('d-none');
                } else {
                    passwordField.attr('type', 'password');
                    $(this).find('.fa-eye').removeClass('d-none');
                    $(this).find('.fa-eye-slash').addClass('d-none');
                }
            });

        });
    </script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/auth/login.blade.php ENDPATH**/ ?>